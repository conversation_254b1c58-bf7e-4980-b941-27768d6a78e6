package com.qudian.idle.customer.center.common.constant;

public class I18nMessageConstant {

    public final static String HIGH_QUANTITY_EMAIL_REQUEST = "yb.high.quantity.email.request";
    public final static String HIGH_QUANTITY_SMS_REQUEST = "yb.high.quantity.sms.request";

    public final static String LIMIT_QUANTITY_SMS_REQUEST = "yb.limit.quantity.sms.request";
    public final static String SEND_MSG_TYPE_ERROR = "yb.send.msg.type.error";
    public final static String AUTH_MSG_TYPE_ERROR = "yb.auth.msg.type.error";
    public final static String SMS_CODE_VALIDATE_ERROR = "yb.sms.code.validate.error";
    public final static String SMS_VERIFICATION_CODE = "yb.sms.verification.code";

    public final static String PASSWORD_NOT_EMPTY = "yb.password.not.empty";
    public final static String USER_NOT_REGISTERED = "yb.user.not.registered";
    public final static String USER_NOT_LOGIN = "yb.user.not.login"; //配置 该操作需要登录的文案

    public final static String USER_ALREADY_LOGIN = "yb.user.already.login"; //配置 该操作需要登录的文案

    public final static String DEL_USER_REASON_ERROR = "yb.del.user.reason.error";
    public final static String VERIFICATION_NOT_EMPTY = "yb.verification.not.empty";
    public final static String MOBILE_NOT_EMPTY = "yb.mobile.not.empty";
    public final static String EMAIL_NOT_EMPTY = "yb.email.not.empty";
    public final static String MOBILE_ALREADY_REGISTERED = "yb.mobile.already.registered";
    public final static String NEW_MOBILE_ALREADY_REGISTERED = "yb.new.mobile.already.registered";
    public final static String EMAIL_ALREADY_REGISTERED = "yb.email.already.registered";

    public final static String USER_ALREADY_REGISTERED = "yb.user.already.registered";
    public final static String USER_CREDENTIALS_NOT_EMPTY = "yb.user.credentials.not.empty";
    public final static String USER_PASSWORDS_NOT_MATCH = "yb.user.two.passwords.not.match";
    public final static String USER_DISABLE = "yb.user.disable";

    public final static String USER_CANCEL = "yb.user.cancel";
    public final static String USER_MOBILE_FORMAT_INCORRECT = "yb.mobile.format.incorrect";

    //玩具ID不可以为空
    public final static String TOY_ID_NOT_EMPTY = "toy.id.empty";
    public final static String TOY_NOT_EXIST = "toy.not.exist";

    public final static String MEMBER_PLAN_EMPTY = "member.plan.empty";

    public final static String MEMBER_PLAN_NOT_EXIST = "member.plan.not.exist";

    public final static String DEFAULT_MEMBER_PLAN_NOT_EXIST = "default.member.plan.not.exist";

    public final static String ORDER_NOT_EXIST = "order.not.exist";

    public final static String NO_REFUNDS_ALLOWED_FOR_ORDERS = "no.refunds.allowed.for.orders";

    public final static String ORDER_PRICE_EXPIRED = "order.price.expired";

    public final static String PAYMENT_METHOD_ERROR = "payment.method.error";

    public final static String USER_EMAIL_FORMAT_INCORRECT = "yb.email.format.incorrect";
    public final static String USER_PASSWORD_INCORRECT = "yb.user.password.incorrect";
    public final static String THREE_ACCOUNT_ALREADY_LINKED = "yb.three.account.already.linked";
    public final static String MOBILE_ALREADY_LINKED = "yb.mobile.already.linked";

    public static final String OLD_PWD_ERROR = "yb.user.pwd.old.error";
    public static final String PWD_NEW_CONSISTENT = "yb.user.pwd.new.consistent";

    public static final String USER_ADDRESS_NAME_INVALID = "yb.user.address.name.invalid";

    public static final String USER_PHOTO_IS_EMPTY = "yb.user.photo.is.empty";

    public static final String USER_NAME_IS_EMPTY = "yb.user.name.is.empty";

    public static final String USER_ADDRESS_ID_IS_EMPTY = "yb.user.address.id.is.empty";

    public static final String USER_ADDRESS_NOT_EXIST = "yb.user.address.not.exist";
    public static final String USER_ADDRESS_REMOVE_RESERVED = "yb.user.address.remove.reserved";

    // 额度相关
    public static final String USER_LIMIT_NOT_EXIST = "yb.user.limit.not.exist";

    public static final String USER_AVAILABLE_LIMIT_DESC = "yb.user.available.limit.desc";
    public static final String USER_CREDIT_AGREEMENT_TITLE = "yb.user.credit.agreement.title";
    public static final String USER_AVAILABLE_LIMIT_TIPS = "yb.user.available.limit.tips";
    public static final String USER_AVAILABLE_LIMIT_NO_ENOUGH = "yb.user.available.limit.no.enough";

    public static final String USER_ORDER_ALREADY_LOCKED_LIMIT = "yb.user.order.already.locked.limit";

    public static final String STORE_STAFF_AUTH_FAILED = "yb.user.staff.auth.failed";
    public static final String USER_CREDIT_SUPPLEMENT_ADD_LIMIT_FAILED = "yb.user.credit.supplement.add.limit.failed";
    public static final String USER_CREDIT_SUPPLEMENT_ADD_FAILED = "yb.user.credit.supplement.failed";
    public static final String USER_CREDIT_SUPPLEMENT_DEGREE_CERTIFICATE_INVALID = "yb.user.credit.supplement.degree.certificate.invalid";
    public static final String USER_CREDIT_SUPPLEMENT_PHONE_NUMBER_AUTH_FAILED = "yb.user.credit.supplement.phone.number.auth.failed";
    public static final String USER_CREDIT_SUPPLEMENT_BANK_TRANSACTIONS_DESC = "yb.user.credit.supplement.bank.transactions.desc";
    public static final String USER_CREDIT_SUPPLEMENT_PAYSLIP_DESC = "yb.user.credit.supplement.payslip.desc";
    public static final String USER_SUPPLEMENT_INVITE_CONTACT_MESSAGE_FORMAT = "yb.user.supplement.invite.contact.message.format";
    public static final String USER_SUPPLEMENT_INVITE_CONTACT_MESSAGE_EXAMPLE_FORMAT = "yb.user.supplement.invite.contact.message.example";
    public static final String USER_SUPPLEMENT_INVITE_CONTACT_FAILED = "yb.user.supplement.invite.contact.failed";



    public static final String USER_CREDIT_RESUBMIT = "Repetitive operation";
    public static final String USER_CREDIT_CARD_UNDOUND = "Setup failed, the card has been unbound";
    public static final String USER_CREDIT_CARD_UNAVAILABLE = "Setup failed, the card is unavailable";

    public static final String USER_CREDIT_CRD_EXPIRED = "Setup failed. The card has expired";

    public static final String USER_CREDIT_DRIVER_LICENSE_NO_USED = "Submission failed. Driver's license number has been used";
    public static final String USER_CREDIT_DNI_USED = "Submission failed. The DNI has been used";
    public static final String USER_CREDIT_PHONE_EMPTY = "yb.user.phone.empty";
    public static final String USER_CREDIT_BANK_COUNTRY_ERROR = "Failed to add, please use your country bank card";
    public static final String NO_RECORD_FOUND = "No record found";
    public static final String USER_CREDIT_TO_BE_AUDIT = "Please wait for the audit result";
    public static final String USER_CREDIT_HAVE_PASSED = "Records that have passed the audit are not allowed to be submitted again";
    public static final String USER_CREDIT_KEEP_ONE_VALID_CARD = "Cannot be unbound, must keep at least one valid card";

    public static final String USER_UN_CREDIT_HEAD_TITLE = "yb.user.no.credit.head.title";
    public static final String USER_UN_CREDIT_TAIL_TITLE = "yb.user.no.credit.tail.title";
    public static final String USER_UN_CREDIT_TAIL_TITLE_NEW = "yb.user.no.credit.tail.title.new";
    public static final String USER_UN_CREDIT_TAIL_TITLE_UNDER_REVIEW = "yb.user.no.credit.tail.title.under.review";
    public static final String USER_INDEX_CREDIT_OVERDUE_HEAD_TITLE = "yb.user.index.credit.overdue.title";
    public static final String USER_INDEX_ORDER_WAITING_PAYMENT_TITLE = "yb.user.index.order.waiting.payment.title";
    public static final String USER_INDEX_ORDER_DELIVERY_TITLE = "yb.user.index.order.delivery.title";
    public static final String USER_INDEX_ORDER_REFUNDING_HEAD_TITLE = "yb.user.index.order.refunding.title";
    public static final String USER_CREDIT_HEAD_TITLE = "yb.user.credit.head.title";
    public static final String USER_CREDIT_TAIL_TITLE = "yb.user.credit.tail.title";
    public static final String USER_CREDIT_SUCCESS_PUSH_APP_TITLE = "yb.user.credit.success.push.app.title";
    public static final String USER_CREDIT_SUCCESS_PUSH_APP_CONTENT = "yb.user.credit.success.push.app.content";
    public static final String USER_CREDIT_SUCCESS_SEND_MESSAGE_CONTENT = "yb.user.credit.success.send.message.content";

    public static final String USER_ADDCREDIT_SUCCESS_PUSH_APP_TITLE = "yb.user.addcredit.success.push.app.title";
    public static final String USER_ADDCREDIT_SUCCESS_PUSH_APP_CONTENT = "yb.user.addcredit.success.push.app.content";
    public static final String USER_ADDCREDIT_SUCCESS_SEND_MESSAGE_CONTENT = "yb.user.addcredit.success.send.message.content";

    public static final String USER_CREDIT_BANK_FAIL_PUSH_APP_TITLE = "yb.user.credit.bankfail.push.app.title";
    public static final String USER_CREDIT_BANK_FAIL_PUSH_APP_CONTENT = "yb.user.credit.bankfail.push.app.content";
    public static final String USER_CREDIT_BANK_FAIL_SEND_MESSAGE_CONTENT = "yb.user.credit.bankfail.send.message.content";

    public static final String USER_CREDIT_OTHER_FAIL_PUSH_APP_TITLE = "yb.user.credit.otherfail.push.app.title";
    public static final String USER_CREDIT_OTHER_FAIL_PUSH_APP_CONTENT = "yb.user.credit.otherfail.push.app.content";
    public static final String USER_CREDIT_OTHER_FAIL_SEND_MESSAGE_CONTENT = "yb.user.credit.otherfail.send.message.content";

    public static final String USER_ADDCREDIT_OTHER_FAIL_PUSH_APP_TITLE = "yb.user.addcredit.otherfail.push.app.title";
    public static final String USER_ADDCREDIT_OTHER_FAIL_PUSH_APP_CONTENT = "yb.user.addcredit.otherfail.push.app.content";
    public static final String USER_ADDCREDIT_OTHER_FAIL_SEND_MESSAGE_CONTENT = "yb.user.addcredit.otherfail.send.message.content";
    public static final String USER_SUPPLEMENT_CONTACT_VALID_RESULT_ACCEPT = "yb.user.supplement.contact.valid.result.accept";
    public static final String USER_SUPPLEMENT_CONTACT_ACCEPT_BT = "yb.user.supplement.invite.contact.accept.bt";
    public static final String USER_SUPPLEMENT_CONTACT_ACCEPT_CONFIRM = "yb.user.supplement.invite.contact.accept.confirm";
    public static final String USER_SUPPLEMENT_CONTACT_REJECT_CONFIRM = "yb.user.supplement.invite.contact.reject.confirm";
    public static final String USER_SUPPLEMENT_CONTACT_REJECT_BT = "yb.user.supplement.invite.contact.reject.bt";
    public static final String USER_SUPPLEMENT_CONTACT_TITLE = "yb.user.supplement.invite.contact.title";
    public static final String USER_SUPPLEMENT_ACCEPT_RESULT = "yb.user.supplement.invite.contact.accept.result";
    public static final String USER_SUPPLEMENT_REJECT_RESULT = "yb.user.supplement.invite.contact.reject.result";
    public static final String USER_CREDIT_SUPPLEMENT_DELETE_FAILED = "yb.user.credit.supplement.delete.failed";
    public static final String USER_CREDIT_INVITE_CONTACT_SHORT_LINK_EXPIRED = "yb.user.credit.invite.contact.short.link.expired";
    public static final String USER_CREDIT_INVITE_CONTACT_MORE_THAN_FIVE = "yb.user.credit.invite.contact.more.than.five";

    public static final String USER_CREDIT_REVIEW_TIP = "yb.user.credit.review.tip";
    public static final String USER_CREDIT_FAIL_TIP = "yb.user.credit.fail.tip";
    public static final String USER_CREDIT_SUCCESS_TIP = "yb.user.credit.success.tip";

    /**
     * 宠物不存在
     */
    public static final String TOY_PET_NOT_EXITS = "toy.pet.not.exits";

    /**
     * 玩偶已被绑定
     */
    public static final String TOY_IS_BOUND = "toy.is.bound";

    /**
     * 玩偶序列号不存在
     */
    public static final String TOY_SN_NOT_EXISTS = "toy.sn.not.exits";


    /**
     * 用户玩偶不存在
     */
    public static final String USER_TOY_NOT_EXISTS = "user.toy.not.exits";

    /**
     * 数据更新失败
     */
    public static final String DATA_UPDATE_FAILED = "system.data.update.failed";


    public static final String QUERY_PARAM_INVALID = "system.query.param.invalid";

    public static final String SYSTEM_ERROR = "system.error";
    public static final String TOY_TYPE_ERROR = "toy.type.error";
    public static final String FAMILY_MEMBER_NOT_EXIST = "family.member.not.exits";

    public static final String PARAMETER_VERIFICATION = "toy.admin.parameter.verification";

    public static final String SECOND_PARTY_ERROR = "toy.admin.second.party.error";

}
