package com.qudian.idle.customer.center.common.utils.biz;

import com.qudian.pdt.toolkit.common.exception.GlobalI18nException;
import org.apache.commons.lang3.StringUtils;

public final class BizUtil {

    private BizUtil(){}

    public static  <T> void requireNonNull(T obj, String message) {
        if (obj == null)
            throw new GlobalI18nException(message, "");
    }

    public static  <T> void requireNonNull(T obj, String message, String i18n) {
        if (obj == null)
            throw new GlobalI18nException(message, i18n);
    }

    public static void requireNonBlank(String obj, String message) {
        if (StringUtils.isBlank(obj))
            throw new GlobalI18nException(message, "");
    }

    public static void requireNonBlank(String obj,String message,  String i18n) {
        if (StringUtils.isBlank(obj))
            throw new GlobalI18nException(message, i18n);
    }

    public static void requireFalse(Boolean obj, String message) {
        if (obj)
            throw new GlobalI18nException(message, "");
    }

    public static void requireFalse(Boolean obj, String message, String i18n) {
        if (obj)
            throw new GlobalI18nException(message, i18n);
    }


}
