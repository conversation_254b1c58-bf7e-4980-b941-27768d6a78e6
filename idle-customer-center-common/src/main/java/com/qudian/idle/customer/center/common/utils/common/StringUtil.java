package com.qudian.idle.customer.center.common.utils.common;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @description <说明该类的目前已经实现的主要功能>
 * @since 2024/1/25
 */
@Slf4j
public class StringUtil {
    /**
     * 整个类的所有string类型字段去空值,trim() 去空
     *
     * @param object
     */
    public static void removeSpaces(Object object) {
        Class<?> clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (int i = 0; i < fields.length; i++) {
            //判断属性是否为字符类型
            String type = fields[i].getGenericType().toString();
            if ("class java.lang.String".equals(type)) {
                removeSpaces(object, clazz, fields[i]);
            }
        }
    }

    public static String StringJson(String stringJson){
        return  stringJson.replace("\\\"", "\"")  // 替换转义双引号为普通双引号
                .replaceFirst("^\"", "")  // 移除开头的双引号
                .replaceFirst("\"$", "");
    }

    private static void removeSpaces(Object object, Class<?> clazz, Field fields) {
        try {
            //获取到当前字段名称，并首字母大写
            String itemName = fields.getName();
            //根据属性名获取到方法名，再根据方法名获取到方法
            Method getMethod = clazz.getMethod("get" + titleCase(itemName));
            Method setMethod = clazz.getMethod("set" + titleCase(itemName), String.class);
            //执行获取到的方法，并传入参数，获取到当前字段对应的属性值
            String val = (String) getMethod.invoke(object);
            //执行set类型方法，为当前字段赋值为.trim()后的新值
            if (CharSequenceUtil.isNotBlank(val)) {
                setMethod.invoke(object, val.trim());
            }
        } catch (Exception e) {
            log.error("[op=StringUtil.removeSpaces error]");
            log.error(e.toString());
        }
    }

    //首字母大写
    public static String titleCase(String s) {
        char[] c = s.toCharArray();
        if ((c[0] >= 'a') && (c[0] <= 'z')) {
            c[0] -= 32;
        }
        return new String(c);
    }
}
