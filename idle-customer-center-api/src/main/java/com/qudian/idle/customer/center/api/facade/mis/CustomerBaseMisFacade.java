package com.qudian.idle.customer.center.api.facade.mis;

import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerShowReqVO;
import com.qudian.idle.customer.center.api.vo.request.mis.CustomerMisPageReqVO;
import com.qudian.idle.customer.center.api.vo.request.mis.CustomerMisTransferReqVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.api.vo.response.mis.CustomerMisPageRespVO;
import com.qudian.idle.customer.center.api.vo.share.IdRequestVO;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import com.qudian.pdt.api.toolkit.vo.response.PagingList;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.facade.mis.CustomerBaseMisFacade</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
public interface CustomerBaseMisFacade {

    /**
     * 添加/编辑客户信息
     * @param reqVO
     * @return {@link BaseResponseDTO }<{@link IdRequestVO }>
     */
    BaseResponseDTO<IdRequestVO> addOrEdit(CustomerEditReqVO reqVO);

    /**
     * 展示客户基础信息
     *
     * @param reqVO
     * @return {@link BaseResponseDTO }<{@link CustomerShowRespVO }>
     */
    BaseResponseDTO<CustomerShowRespVO> show(CustomerShowReqVO reqVO);

    /**
     * 客户列表
     *
     * @param reqVO
     * @return {@link BaseResponseDTO }<{@link PagingList }<{@link CustomerMisPageRespVO }>>
     */
    BaseResponseDTO<PagingList<CustomerMisPageRespVO>> page(CustomerMisPageReqVO reqVO);

    /**
     * 流转客户关系
     *
     * @param reqVO
     * @return {@link BaseResponseDTO }<{@link BaseResponseVO }>
     */
    BaseResponseDTO<BaseResponseVO> transfer(CustomerMisTransferReqVO reqVO);
}
