package com.qudian.idle.customer.center.api.vo;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.reader.ObjectReader;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * Custom deserializer for OptionValues to handle JSON deserialization properly
 */
public class OptionValuesDeserializer implements ObjectReader<OptionValues> {

    @Override
    public OptionValues readObject(JSONReader jsonReader, Type fieldType, Object fieldName, long features) {
        if (jsonReader.isNull()) {
            return null;
        }
        Object value = jsonReader.readAny();
        if (value == null) {
            return new OptionValues();
        }
        if (value instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) value;
            List<OptionValues.OptionRecord> records = new ArrayList<>();

            for (Object item : jsonArray) {
                if (item instanceof JSONObject) {
                    JSONObject jsonObj = (JSONObject) item;
                    Integer code = jsonObj.getInteger("code");
                    String title = jsonObj.getString("title");
                    records.add(new OptionValues.OptionRecord(code, title));
                } else if (item instanceof OptionValues.OptionRecord) {
                    records.add((OptionValues.OptionRecord) item);
                }
            }

            return new OptionValues(records);
        } else if (value instanceof List) {
            List<?> list = (List<?>) value;
            List<OptionValues.OptionRecord> records = new ArrayList<>();

            for (Object item : list) {
                if (item instanceof JSONObject) {
                    JSONObject jsonObj = (JSONObject) item;
                    Integer code = jsonObj.getInteger("code");
                    String title = jsonObj.getString("title");
                    records.add(new OptionValues.OptionRecord(code, title));
                } else if (item instanceof OptionValues.OptionRecord) {
                    records.add((OptionValues.OptionRecord) item);
                }
            }
            return new OptionValues(records);
        }
        return new OptionValues();
    }
}
