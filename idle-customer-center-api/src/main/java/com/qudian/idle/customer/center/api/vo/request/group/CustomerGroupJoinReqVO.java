package com.qudian.idle.customer.center.api.vo.request.group;

import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerGroupJoinReqVO  extends BaseRequestVO {
    private String customerId;

    private List<String> customGroupList;


}
