package com.qudian.idle.customer.center.api.vo;

import com.alibaba.fastjson2.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.vo.OptionValues</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
public class OptionValues extends ArrayList<OptionValues.OptionRecord> {

    @Data
    @AllArgsConstructor
    public static class OptionRecord implements Serializable {
        private static final long serialVersionUID = -101678720028544738L;
        private Integer code;
        private String title;
    }

    public OptionValues() {
        super();
    }

    public OptionValues(List<OptionRecord> list) {
        super(null != list ? list : Collections.emptyList());
    }

    public Integer code() {
        return this.isEmpty() ? null : this.get(0).getCode();
    }

    public String title() {
        return this.isEmpty() ? null : this.get(0).getTitle();
    }

    public static OptionValues of(Integer code, String title) {
        return new OptionValues(List.of(new OptionRecord(code, title)));
    }

    /**
     * 严格校验单值
     *
     * @return {@link Integer }
     */
    public Integer requireSingleCode() {
        if (this.isEmpty()) {
            return null;
        }
        if (this.size() > 1) {
            throw new IllegalStateException("Expect single code but got " + this.size() + " records");
        }
        return this.get(0).getCode();
    }

    public List<Integer> codes() {
        List<Integer> list = new ArrayList<>(this.size());
        for (OptionRecord r : this) {
            if (null == r || null == r.getCode()) {
                continue;
            }
            list.add(r.getCode());
        }
        return list;
    }

    public String toJson() {
        return JSON.toJSONString(this);
    }

    public static OptionValues fromJson(String json) {
        List<OptionRecord> list = JSON.parseArray(json, OptionRecord.class);
        return new OptionValues(list);
    }
}
