package com.qudian.idle.customer.center.api.facade.inside;

import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyRespVO;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.facade.base.CustomerBaseAppFacade</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
public interface CustomerPropertyInsideFacade {


    BaseResponseDTO<CustomerPropertyRespVO> getSearchPropertyList(BaseRequestVO requestVO);
}
