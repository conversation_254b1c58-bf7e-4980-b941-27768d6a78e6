package com.qudian.idle.customer.center.api.vo.request.group;

import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> Huang
 * @date 2025/8/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerGroupSaveReqVO  extends BaseRequestVO {
    /**
     * 组别Id（修改时带上）
     */
    private String code;
    /**
     * 组别名称
     */
    private String title;
}
