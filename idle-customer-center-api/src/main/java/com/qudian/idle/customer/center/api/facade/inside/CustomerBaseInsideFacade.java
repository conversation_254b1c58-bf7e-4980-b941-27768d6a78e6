package com.qudian.idle.customer.center.api.facade.inside;

import com.qudian.idle.customer.center.api.vo.request.inside.CustomerBatchQueryInHomeReqVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerInfoInHomeRespVO;
import com.qudian.lme.common.dto.BaseResponseDTO;

import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.facade.inside.CustomerBaseInsideFacade</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
public interface CustomerBaseInsideFacade {
    /**
     * 批量查询客户信息（首页部分）
     * @param reqVO
     * @return
     */
    BaseResponseDTO<List<CustomerInfoInHomeRespVO>> batchQueryCustomerInfoInHome(CustomerBatchQueryInHomeReqVO reqVO);
}
