package com.qudian.idle.customer.center.api.enums;

import lombok.AllArgsConstructor;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.enums.CustomerPreferBrandEnum</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/14
 */
@AllArgsConstructor
public enum CustomerPreferBrandEnum {
    BAG("bag", "包袋品牌偏好"),
    JEWEL("jewel", "珠宝品牌偏好"),
    WATCHES("watches", "手表品牌偏好");

    public final String code;
    public final String title;
}
