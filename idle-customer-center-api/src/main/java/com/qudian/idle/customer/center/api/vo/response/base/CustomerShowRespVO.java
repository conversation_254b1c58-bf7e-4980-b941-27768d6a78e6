package com.qudian.idle.customer.center.api.vo.response.base;

import com.qudian.idle.customer.center.api.vo.OptionValues;
import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CustomerShowRespVO extends BaseResponseVO {
    private static final long serialVersionUID = 918180726654554290L;

    private CustomerShowBaseRespVO base;
    private CustomerShowExtRespVO preference;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerShowBaseRespVO implements Serializable {
        private static final long serialVersionUID = 6741371488551945754L;

        private Long id;
        private String name;
        private String mobile;
        private String type;
        private String wechatId; //客户微信号
        private OptionValues gender; //性别 1(男), 2(女)
        private OptionValues ageGroup; //客户年龄段: 1(<24), 2(25-34), 3(35-44), 4(45-54), 5(55+)
        private OptionValues source; //客户来源: 1(线上引流), 2(线下渠道)
        private OptionValues homeVisitStatus; //是否接受上门: 1(接受上门), 2(不接受上门)
        private OptionValues contactMode; //首选沟通方式【多选】
        private CustomerShowMembershipReqVO membership;
        private CustomerShowAddressReqVO address;
        private String remark; //备注
        private String images; //备注图
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerShowMembershipReqVO implements Serializable {
        private OptionValues outputIntention; //客户出售意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定)
        private OptionValues membershipType; //会员类型: 1(普通), 2（VIP会员）
        private OptionValues purchaseIntention; //客户购买意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定)
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerShowAddressReqVO implements Serializable {
        private String province; //地址-省份
        private String city; //地址-城市
        private String district; //地址-区域
        private String detailAddress; //地址-详细地址
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class CustomerShowExtRespVO implements Serializable {
        private static final long serialVersionUID = 3963234033724077384L;

        private OptionValues preferType; //偏好类型
        private String bag; //包袋品牌偏好
        private String jewel;  //珠宝品牌偏好
        private String watches;  //手表品牌偏好
    }
}

