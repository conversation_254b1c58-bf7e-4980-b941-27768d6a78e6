package com.qudian.idle.customer.center.api.facade.app;

import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupJoinReqVO;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupListReqVO;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupSaveReqVO;
import com.qudian.idle.customer.center.api.vo.response.group.CustomerGroupJoinRespVO;
import com.qudian.idle.customer.center.api.vo.response.group.CustomerGroupListRespVO;
import com.qudian.idle.customer.center.api.vo.share.IdRequestVO;
import com.qudian.lme.common.dto.BaseResponseDTO;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.facade.base.CustomerBaseAppFacade</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
public interface CustomerGroupAppFacade {

    /**
     *  加入客户到指定组别
     * @param customerGroupJoinReqVO
     * @return
     */
    BaseResponseDTO<IdRequestVO> join(CustomerGroupJoinReqVO customerGroupJoinReqVO);


    /**
     * 添加、编辑组别名称
     */
    BaseResponseDTO<IdRequestVO> save(CustomerGroupSaveReqVO customerGroupSaveReqVO);


    /**
     * 获取客户组别
     */
    BaseResponseDTO<CustomerGroupListRespVO> list(CustomerGroupListReqVO customerGroupListReqVO);

}
