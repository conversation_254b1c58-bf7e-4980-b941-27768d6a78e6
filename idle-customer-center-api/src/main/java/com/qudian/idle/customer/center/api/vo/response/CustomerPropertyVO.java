package com.qudian.idle.customer.center.api.vo.response;

import com.alibaba.fastjson2.JSON;
import com.qudian.idle.customer.center.api.vo.share.Property;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Huang
 * @date 2025/8/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomerPropertyVO implements Serializable {
    //客户年龄段: 1(<24), 2(25-34), 3(35-44), 4(45-54), 5(55+)
    private List<Property> ageGroup;
    //客户性别: 1(男), 2(女)
    private List<Property> gender;
    //首选沟通方式： 1(微信沟通), 2(电话沟通)、3（线下沟通）
    private List<Property> contactMode;
    //客户来源: 1(线上引流), 2(线下渠道)
    private List<Property> source;
    //会员类型: 1(普通), 2（VIP会员)
    private List<Property> membershipType;
    //客户出售意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定)
    private List<Property> outputIntention;
    //客户购买意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定)
    private List<Property> purchaseIntention;
    //偏好类型: 1(包袋), 2(珠宝), 3(手表)
    private List<Property> preferType;
    //包袋标签：Hermès、Louis Vuitton、Dior、Chanel
    private List<Property> bagTag;
    //珠宝标签：Cartier、Van Cleef & Arpels、Bvlgari、Harry Winston
    private List<Property> jewelTag;
    //手表标签：Patek Philippe、Rolex、Audemars Piguet、Breguet
    private List<Property> watchesTag;
    //客户类型：organization组织、personal个人	
    private List<Property> customerType;
    //是否接受上门: 1接受, 0 不接受	
    private List<Property> homeVisitStatus;

    private List<Property> customGroup;

    public static void main(String[] args) {
        CustomerPropertyVO customerPropertyVO = new CustomerPropertyVO();

        // 设置客户年龄段
        customerPropertyVO.setAgeGroup(List.of(
                new Property("1", "≤24 岁"),
                new Property("2", "25-34 岁"),
                new Property("3", "35-44 岁"),
                new Property("4", "45-54 岁"),
                new Property("5", "55岁+")
        ));

        // 设置客户性别
        customerPropertyVO.setGender(List.of(
                new Property("1", "男"),
                new Property("2", "女")
        ));

        // 设置首选沟通方式
        customerPropertyVO.setContactMode(List.of(
                new Property("1", "微信沟通"),
                new Property("2", "电话沟通"),
                new Property("3", "线下沟通")
        ));

        // 设置客户来源
        customerPropertyVO.setSource(List.of(
                new Property("1", "线上引流"),
                new Property("2", "线下渠道")
        ));

        // 设置会员类型
        customerPropertyVO.setMembershipType(List.of(
                new Property("1", "普通"),
                new Property("2", "VIP会员")
        ));

        // 设置客户出售意向
        customerPropertyVO.setOutputIntention(List.of(
                new Property("1", "高意向"),
                new Property("2", "中意向"),
                new Property("3", "低意向"),
                new Property("4", "无意向"),
                new Property("5", "不确定")
        ));

        // 设置客户购买意向
        customerPropertyVO.setPurchaseIntention(List.of(
                new Property("1", "高意向"),
                new Property("2", "中意向"),
                new Property("3", "低意向"),
                new Property("4", "无意向"),
                new Property("5", "不确定")
        ));

        // 设置偏好类型
        customerPropertyVO.setPreferType(List.of(
                new Property("1", "包袋"),
                new Property("2", "珠宝"),
                new Property("3", "手表")
        ));

        // 设置包袋标签
        customerPropertyVO.setBagTag(List.of(
                new Property("Hermès", "爱马仕"),
                new Property("Louis Vuitton", "路易威登"),
                new Property("Dior", "迪奥"),
                new Property("Chanel", "香奈儿")
        ));

        // 设置珠宝标签
        customerPropertyVO.setJewelTag(List.of(
                new Property("Cartier", "卡地亚"),
                new Property("Van Cleef & Arpels", "梵克雅宝"),
                new Property("Bvlgari", "宝格丽"),
                new Property("Harry Winston", "海瑞温斯顿")
        ));

        // 设置手表标签
        customerPropertyVO.setWatchesTag(List.of(
                new Property("Patek Philippe", "百达翡丽"),
                new Property("Rolex", "劳力士"),
                new Property("Audemars Piguet", "爱彼"),
                new Property("Breguet", "宝玑")
        ));

        // 设置客户类型
        customerPropertyVO.setCustomerType(List.of(
                new Property("organization", "组织"),
                new Property("personal", "个人")
        ));

        // 设置是否接受上门
        customerPropertyVO.setHomeVisitStatus(List.of(
                new Property("1", "接受"),
                new Property("0", "不接受")
        ));


        System.out.println(JSON.toJSONString(CustomerPropertyRespVO.builder().properties(customerPropertyVO).build()));
    }
}
