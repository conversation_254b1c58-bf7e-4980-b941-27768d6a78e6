package com.qudian.idle.customer.center.api.vo.request.mis;

import com.qudian.pdt.api.toolkit.vo.request.PageRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.vo.request.inside.CustomerBatchQueryInHome</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CustomerMisPageReqVO extends PageRequestVO {
    private Long id;
    private String name;
    private String mobile;
    private Boolean hasWechatId;
    private Integer membershipType;
    private Integer outputIntention;
    private Integer purchaseIntention;
}
