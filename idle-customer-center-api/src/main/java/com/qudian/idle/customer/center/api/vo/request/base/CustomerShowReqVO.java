package com.qudian.idle.customer.center.api.vo.request.base;

import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.vo.request.base.CustomerEditVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerShowReqVO extends BaseRequestVO {
    private static final long serialVersionUID = -40915276506458728L;

    private Long customerId;
}
