package com.qudian.idle.customer.center.infrastructure.assembler;

import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerExtPO;
import org.mapstruct.Mapper;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.assembler.CustomerExtStruct</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Mapper
public interface CustomerExtStruct {

    CustomerExtPO preVO2PO(CustomerEditReqVO.CustomerEditExtReqVO vo);
}
