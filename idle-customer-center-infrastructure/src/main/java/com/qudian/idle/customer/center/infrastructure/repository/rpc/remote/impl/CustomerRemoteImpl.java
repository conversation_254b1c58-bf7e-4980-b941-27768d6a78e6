package com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.impl;

import com.alibaba.fastjson.JSON;
import com.qudian.idle.crm.web.api.dto.base.CommonRespDTO;
import com.qudian.idle.crm.web.api.dto.req.CustomerInfoReqDTO;
import com.qudian.idle.crm.web.api.service.CustomerFacade;
import com.qudian.idle.customer.center.common.constant.I18nMessageConstant;
import com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.CustomerRemote;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.toolkit.common.enums.ExceptionEnum;
import com.qudian.pdt.toolkit.common.exception.GlobalI18nException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Huang
 * @date 2025/8/15
 */
@Component
@Slf4j
public class CustomerRemoteImpl implements CustomerRemote {

    @DubboReference(protocol = "dubbo", version = "1.0.0", timeout = 60000)
    // @org.apache.dubbo.config.annotation.DubboReference(url = "dubbo://47.92.119.248:30195",version = "1.0.0")
    private CustomerFacade customerFacade;

    @Value("${customer.noticeUpdate.enableFlag:false}")
    private Boolean noticeUpdateEnableFlag;

    @Override
    public CommonRespDTO<String> updateCustomerInfo(CustomerInfoReqDTO req) {
        if(!noticeUpdateEnableFlag){
            log.info("通知下游创建/更新客户信息,开关未开启");
            return null;
        }

        log.info("op=start_通知下游创建/更新客户信息.updateCustomerInfo, req={}", JSON.toJSONString(req));
        CustomerInfoReqDTO requests = new CustomerInfoReqDTO();
        CommonRespDTO<String> resp = customerFacade.updateCustomerInfo(requests);
        log.info("通知下游创建/更新客户信息 请求响应:{}", JSON.toJSONString(resp));
        if (!resp.isSuccess()) {
            log.error("通知下游创建/更新客户信息,响应失败:{}", JSON.toJSONString(resp));
            throw new GlobalI18nException(ExceptionEnum.SECOND_PARTY_ERROR, I18nMessageConstant.SYSTEM_ERROR);
        }
        return null;
    }
}
