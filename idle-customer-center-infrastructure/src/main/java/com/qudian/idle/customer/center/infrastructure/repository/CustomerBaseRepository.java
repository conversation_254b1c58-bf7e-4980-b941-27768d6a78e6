package com.qudian.idle.customer.center.infrastructure.repository;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.qudian.idle.crm.web.api.dto.req.CustomerInfoReqDTO;
import com.qudian.idle.crm.web.api.enums.BizCodeEnum;
import com.qudian.idle.crm.web.api.enums.SourceEnum;
import com.qudian.idle.customer.center.infrastructure.repository.database.mapper.CustomerGroupMapper;
import com.qudian.idle.customer.center.infrastructure.repository.database.mapper.base.CustomerBaseMapper;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerBasePO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.group.CustomerGroupPO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.group.CustomerGroupRelationPO;
import com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.CustomerRemote;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.repository.database.CustomerBaseRepository</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Repository
@Slf4j
public class CustomerBaseRepository extends ServiceImpl<CustomerBaseMapper, CustomerBasePO> {
    @Resource
    private CustomerRemote customerRemote;

    @Resource
    private CustomerGroupRepository customerGroupRepository;

    @Resource
    private CustomerGroupRelationRepository customerGroupRelationRepository;

    @Resource
    private CustomerBaseMapper customerBaseMapper;

    public Optional<CustomerBasePO> selectById(Long id) {
        CustomerBasePO customerGroupPO = customerBaseMapper.selectById(id);
        return Optional.ofNullable(customerGroupPO);
    }



    public void noticeUpdateCustomer(Long customerId) {
        log.info("op=start_CustomerBaseRepository.noticeUpdateCustomer, customerId={}", customerId);
        Optional<CustomerBasePO> customerBasePOOptional = selectById(customerId);
        if(!customerBasePOOptional.isPresent()){
            log.warn("customerBasePO is empty, customerId:{}", customerId);
            return ;
        }
        CustomerBasePO customerBasePO = getById(customerId);
        CustomerInfoReqDTO req = new CustomerInfoReqDTO();
        req.setAccountManagerId(String.valueOf(customerBasePO.getRelatedManagerId()));
        req.setCustomerId(String.valueOf(customerId));
        req.setAssignTime(customerBasePO.getRelatedManagerUpdatedTime());
        req.setBuyIntentStatus(customerBasePO.getPurchaseIntention());
        req.setSellIntentStatus(customerBasePO.getOutputIntention());
        req.setMobileNumber(customerBasePO.getMobile());
        req.setBizCode(BizCodeEnum.idle.getBizCode());
        req.setSource(SourceEnum.USER.getCode());

        Optional<CustomerGroupRelationPO> customerGroupRelationPOOptional = customerGroupRelationRepository.selectByManageIdAndCustomerId(customerBasePO.getRelatedManagerId(), customerId);
        String groupIds = customerGroupRelationPOOptional.get().getGroupId();
        List<String> groupIdList = JSON.parseArray(groupIds, String.class);
//        List<CustomerGroupPO> customerGroupPOList = customerGroupRepository.queryList(CustomerGroupPO.builder().managerId(customerBasePO.getRelatedManagerId()).build());
//        List<String> customerGroups = customerGroupPOList.stream().map(t -> String.valueOf(t.getId())).collect(Collectors.toList());
        req.setCustomGroups(groupIdList);
        customerRemote.updateCustomerInfo(req);
    }

    public boolean updateRelatedManagerId(Long customerId, Long managerId) {
        CustomerBasePO existBasePO = customerBaseMapper.selectById(customerId);
        if (existBasePO.getRelatedManagerId().equals(managerId)) {
            return false;
        }
        existBasePO.setRelatedManagerId(managerId);
        return SqlHelper.retBool(customerBaseMapper.updateById(existBasePO));
    }
}
