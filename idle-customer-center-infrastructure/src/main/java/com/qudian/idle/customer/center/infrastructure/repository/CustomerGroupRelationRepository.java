package com.qudian.idle.customer.center.infrastructure.repository;

/**
 * <AUTHOR> <PERSON>
 * @date 2025/8/14
 */

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.qudian.idle.customer.center.infrastructure.repository.database.mapper.CustomerGroupRelationMapper;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.group.CustomerGroupRelationPO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description 奢侈品-客户关联组别 Repository
 * @date 2025-08-14
 */
@Repository
public class CustomerGroupRelationRepository {
    @Resource
    private CustomerGroupRelationMapper customerGroupRelationMapper;

    public Optional<CustomerGroupRelationPO> selectById(Long id) {
        CustomerGroupRelationPO customerGroupRelationPO = customerGroupRelationMapper.selectById(id);
        return Optional.ofNullable(customerGroupRelationPO);
    }


    public Optional<CustomerGroupRelationPO> selectByManageIdAndCustomerId(Long manageId, Long customerId) {
        QueryWrapper<CustomerGroupRelationPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CustomerGroupRelationPO::getManagerId, manageId);
        queryWrapper.lambda().eq(CustomerGroupRelationPO::getCustomerId, customerId);
        queryWrapper.last("limit 1");
        CustomerGroupRelationPO customerGroupRelationPO = customerGroupRelationMapper.selectOne(queryWrapper);
        return Optional.ofNullable(customerGroupRelationPO);
    }

    public boolean deleteById(Long id) {
        return SqlHelper.retBool(customerGroupRelationMapper.deleteById(id));
    }

//	public Optional<CustomerGroupRelationPO> selectByName(String name) {
//		QueryWrapper<CustomerGroupRelationPO> queryWrapper = new QueryWrapper<>();
//		queryWrapper.lambda().eq(CustomerGroupRelationPO::getName, name);
//			CustomerGroupRelationPO customerGroupRelationPO = customerGroupRelationMapper.selectOne(queryWrapper);
//		return Optional.ofNullable(customerGroupRelationPO);
//	}

//    public List<CustomerGroupRelationListRespVO> queryList(CustomerGroupRelationListReqVO listReqVO) {
//        return customerGroupRelationMapper.queryList(listReqVO);
//    }

    public boolean insert(CustomerGroupRelationPO customerGroupRelationPO) {
        return SqlHelper.retBool(customerGroupRelationMapper.insert(customerGroupRelationPO));
    }

    public boolean update(CustomerGroupRelationPO customerGroupRelationPO) {
        return SqlHelper.retBool(customerGroupRelationMapper.updateById(customerGroupRelationPO));
    }

}