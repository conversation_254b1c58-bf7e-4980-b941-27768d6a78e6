package com.qudian.idle.customer.center.infrastructure.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.customer.center.infrastructure.repository.database.mapper.base.CustomerExtMapper;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerExtPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.repository.database.CustomerBaseRepository</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Repository
@Slf4j
public class CustomerExtRepository extends ServiceImpl<CustomerExtMapper, CustomerExtPO> {
}
