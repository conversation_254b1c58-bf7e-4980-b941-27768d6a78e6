package com.qudian.idle.customer.center.infrastructure.repository.database.mapper;

/**
 * <AUTHOR> <PERSON>
 * @date 2025/8/14
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupListReqVO;
import com.qudian.idle.customer.center.api.vo.response.group.CustomerGroupListRespVO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.group.CustomerGroupPO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @description 奢侈品-客户组别 Mapper
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface CustomerGroupMapper extends BaseMapper<CustomerGroupPO> {
    @Select({"<script>",
            "SELECT * from customer_group t where 1=1 ",
            "and t.delete_flag=0 ",
//			DATE_FORMAT(t.created_time,'%Y-%m-%d %H:%i:%s') created_time
//			"<if test='@org.apache.commons.lang3.StringUtils@isNotBlank(name)' > and t.name LIKE CONCAT('%',#{name},'%') </if>",
          "<if test='managerId!=null' > and t.manager_id = #{managerId} </if>",
//			"<if test='createdStartTime != null' > and t.created_time &gt;= #{createdStartTime} </if>",
//			"<if test='createdEndTime != null' > and t.created_time &lt;= #{createdEndTime} </if>",
//			"<if test='!@org.springframework.util.CollectionUtils@isEmpty(idList)' > ",
//			" t.id IN"+ "<foreach collection='idList' item='id' open='(' separator=',' close=')'> #{id}</foreach>",
//			"</if>",
            "order by t.id desc",
            "</script>"})
    List<CustomerGroupPO> queryList(CustomerGroupPO listReqVO);

}