package com.qudian.idle.customer.center.infrastructure.repository;

/**
 * <AUTHOR> <PERSON>
 * @date 2025/8/14
 */

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupListReqVO;
import com.qudian.idle.customer.center.api.vo.response.group.CustomerGroupListRespVO;
import com.qudian.idle.customer.center.infrastructure.repository.database.mapper.CustomerGroupMapper;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.group.CustomerGroupPO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


/**
 * @description 奢侈品-客户组别 Repository
 * <AUTHOR>
 * @date 2025-08-14
 */
@Repository
public class  CustomerGroupRepository{
    @Resource
    private CustomerGroupMapper customerGroupMapper;

    public Optional<CustomerGroupPO> selectById(Long id) {
        CustomerGroupPO customerGroupPO = customerGroupMapper.selectById(id);
        return Optional.ofNullable(customerGroupPO);
    }



    public boolean deleteById(Long id) {
        return SqlHelper.retBool(customerGroupMapper.deleteById(id));
    }

	public Optional<CustomerGroupPO> selectByManagerIdAndName(Long managerId, String name) {
		QueryWrapper<CustomerGroupPO> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(CustomerGroupPO::getManagerId, managerId);
		queryWrapper.lambda().eq(CustomerGroupPO::getGroupName, name);
        queryWrapper.last("limit 1");
        CustomerGroupPO customerGroupPO = customerGroupMapper.selectOne(queryWrapper);
		return Optional.ofNullable(customerGroupPO);
	}

    public List<CustomerGroupPO> queryList(CustomerGroupPO listReqVO) {
        return customerGroupMapper.queryList(listReqVO);
    }

    public boolean insert(CustomerGroupPO customerGroupPO) {
        return SqlHelper.retBool(customerGroupMapper.insert(customerGroupPO));
    }

    public boolean update(CustomerGroupPO customerGroupPO) {
        return SqlHelper.retBool(customerGroupMapper.updateById(customerGroupPO));
    }

}