package com.qudian.idle.customer.center.business.core;

import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupListReqVO;
import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;

import java.util.Objects;

/**
 * <AUTHOR> Huang
 * @date 2025/8/14
 */
public class ContextUtil {

    //Todo: @hfq 从上下文获取
    public static Long getUserId(){
        return 0l;
    }


    public static Long getUserId(BaseRequestVO baseRequestVO){
        if(Objects.nonNull(baseRequestVO.getUserId())){
            return baseRequestVO.getUserId();
        }
        return 0l;
    }

    public static String getUserName(){
        return "defaultUser";
    }

}
