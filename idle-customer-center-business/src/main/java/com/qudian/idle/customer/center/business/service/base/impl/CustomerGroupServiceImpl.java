package com.qudian.idle.customer.center.business.service.base.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupJoinReqVO;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupListReqVO;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupSaveReqVO;
import com.qudian.idle.customer.center.api.vo.response.group.CustomerGroupListRespVO;
import com.qudian.idle.customer.center.api.vo.share.IdRequestVO;
import com.qudian.idle.customer.center.api.vo.share.Property;
import com.qudian.idle.customer.center.business.core.ContextUtil;
import com.qudian.idle.customer.center.business.service.base.CustomerGroupService;
import com.qudian.idle.customer.center.common.utils.biz.BizUtil;
import com.qudian.idle.customer.center.common.utils.common.StringUtil;
import com.qudian.idle.customer.center.infrastructure.repository.CustomerBaseRepository;
import com.qudian.idle.customer.center.infrastructure.repository.CustomerGroupRelationRepository;
import com.qudian.idle.customer.center.infrastructure.repository.CustomerGroupRepository;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.group.CustomerGroupPO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.group.CustomerGroupRelationPO;
import com.qudian.pdt.toolkit.common.exception.BizException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> Huang
 * @date 2025/8/14
 */
@Slf4j
@Service
public class CustomerGroupServiceImpl implements CustomerGroupService {

    @Resource
    private CustomerGroupRepository customerGroupRepository;
    @Resource
    private CustomerGroupRelationRepository customerGroupRelationRepository;
    @Resource
    private CustomerBaseRepository customerBaseRepository;
    @Value("${customer.defaultGroupName:自定义组别1}")
    private String defaultGroupName;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IdRequestVO save(CustomerGroupSaveReqVO saveReqVO) {
        StringUtil.removeSpaces(saveReqVO);
        saveVerify(saveReqVO);
        return doSave(saveReqVO);
    }

    @Override
    public IdRequestVO join(CustomerGroupJoinReqVO customerGroupJoinReqVO) {
        joinVerify(customerGroupJoinReqVO);

        Long manageId = ContextUtil.getUserId(customerGroupJoinReqVO);
        long customerId = Long.parseLong(customerGroupJoinReqVO.getCustomerId());
        String groupIds = "";
        if (CollUtil.isNotEmpty(customerGroupJoinReqVO.getCustomGroupList())) {
            groupIds = JSON.toJSONString(customerGroupJoinReqVO.getCustomGroupList());
        } else {
            groupIds = "[]";
        }

        Optional<CustomerGroupRelationPO> relationPOOptional = customerGroupRelationRepository.selectByManageIdAndCustomerId(manageId, customerId);
        CustomerGroupRelationPO relationPO = null;
        if (relationPOOptional.isPresent()) {
            relationPO = relationPOOptional.get();
            relationPO.setGroupId(groupIds);
            customerGroupRelationRepository.update(relationPO);
        } else {
            relationPO = CustomerGroupRelationPO.builder().customerId(customerId).groupId(groupIds).build();
            relationPO.setManagerId(manageId);
            customerGroupRelationRepository.insert(relationPO);
        }

        customerBaseRepository.noticeUpdateCustomer(customerId);
        return IdRequestVO.builder().id(relationPO.getId()).build();
    }

    @Override
    public CustomerGroupListRespVO list(CustomerGroupListReqVO customerGroupListReqVO) {

        CustomerGroupListRespVO customerGroupListRespVO = new CustomerGroupListRespVO();
        Long manageId = ContextUtil.getUserId(customerGroupListReqVO);
        List<CustomerGroupPO> customerGroupPOList = customerGroupRepository.queryList(CustomerGroupPO.builder().managerId(manageId).build());
        if (CollUtil.isNotEmpty(customerGroupPOList)) {
            List<Property> customGroupPropertyList = new ArrayList<>();
            for (CustomerGroupPO groupPO : customerGroupPOList) {
                customGroupPropertyList.add(Property.builder().code(String.valueOf(groupPO.getId())).title(groupPO.getGroupName()).build());
            }
            customerGroupListRespVO.setCustomGroup(customGroupPropertyList);

        } else {
            log.info("customerGroupPOList is empty.add default");
            customerGroupRepository.insert(CustomerGroupPO.builder().managerId(manageId).groupName(defaultGroupName).build());
            return list(customerGroupListReqVO);
        }
        return customerGroupListRespVO;
    }

    private void joinVerify(CustomerGroupJoinReqVO customerGroupJoinReqVO) {
        Objects.requireNonNull(customerGroupJoinReqVO.getCustomerId(), "customerId must not be null.");
//        if(CollUtil.isEmpty(customerGroupJoinReqVO.getCustomGroupListList())){
//            throw new BizException("customGroupListList must not be null.");
//        }
        try {
            if (StrUtil.isNotBlank(customerGroupJoinReqVO.getCustomerId())) {
                Long.parseLong(customerGroupJoinReqVO.getCustomerId());
            }
        } catch (Exception e) {
            throw new BizException("customerId无法转化为数字");
        }
    }


    private IdRequestVO doSave(CustomerGroupSaveReqVO saveReqVO) {
        if (StrUtil.isBlank(saveReqVO.getCode())) {
            CustomerGroupPO customerGroupPO = insert(saveReqVO);
            return IdRequestVO.builder().id(customerGroupPO.getId()).build();
        } else {
            CustomerGroupPO customerGroupPO = update(saveReqVO);
            return IdRequestVO.builder().id(customerGroupPO.getId()).build();
        }
    }


    private CustomerGroupPO insert(CustomerGroupSaveReqVO saveReqVO) {
        CustomerGroupPO customerGroupPO = CustomerGroupPO.builder().groupName(saveReqVO.getTitle()).build();
        customerGroupPO.setManagerId(ContextUtil.getUserId());
        boolean successFlag = customerGroupRepository.insert(customerGroupPO);
        return customerGroupPO;
    }


    private CustomerGroupPO update(CustomerGroupSaveReqVO saveReqVO) {
        CustomerGroupPO customerGroupPO = CustomerGroupPO.builder().id(Long.parseLong(saveReqVO.getCode())).groupName(saveReqVO.getTitle()).build();
        Long id = customerGroupPO.getId();
//        customerGroupPO.setUpdatedId(SsoUtil.getUserId()).setUpdatedName(SsoUtil.getUserName());
        customerGroupRepository.selectById(id).orElseThrow(() -> new BizException(String.format("No record found:%s.", id)));
        boolean updateSuccessFlag = customerGroupRepository.update(customerGroupPO);
        return customerGroupPO;
    }

    private void saveVerify(CustomerGroupSaveReqVO saveReqVO) {
        Optional.ofNullable(saveReqVO).orElseThrow(() -> new BizException("saveReqVO must not be null."));
//		BizUtil.requireFalse(!StatusEnum.contains(saveReqVO.getStatus()),"Status value error.");
        Objects.requireNonNull(saveReqVO.getTitle(), "title must not be null.");
        try {
            if (StrUtil.isNotBlank(saveReqVO.getCode())) {
                Long.parseLong(saveReqVO.getCode());
            }
        } catch (Exception e) {
            throw new BizException("code无法转化为数字");
        }
        Long manageId = ContextUtil.getUserId(saveReqVO);
        boolean isInsert = Objects.isNull(saveReqVO.getCode());
        Optional<CustomerGroupPO> customerGroupPOOptional = customerGroupRepository.selectByManagerIdAndName(manageId, saveReqVO.getTitle());
        boolean nameExist = customerGroupPOOptional.isPresent() && (isInsert || !String.valueOf(customerGroupPOOptional.get().getId()).equals((saveReqVO.getCode())));
        BizUtil.requireFalse(nameExist, "组别名已存在.", "组别名已存在");
    }
}
