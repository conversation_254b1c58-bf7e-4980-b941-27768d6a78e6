package com.qudian.idle.customer.center.business.service.base;

import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupJoinReqVO;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupListReqVO;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupSaveReqVO;
import com.qudian.idle.customer.center.api.vo.response.group.CustomerGroupJoinRespVO;
import com.qudian.idle.customer.center.api.vo.response.group.CustomerGroupListRespVO;
import com.qudian.idle.customer.center.api.vo.share.IdRequestVO;

/**
 * <AUTHOR> Huang
 * @date 2025/8/14
 */
public interface CustomerGroupService {
    IdRequestVO save(CustomerGroupSaveReqVO customerGroupSaveReqVO);

    IdRequestVO join(CustomerGroupJoinReqVO customerGroupJoinReqVO);

    CustomerGroupListRespVO list(CustomerGroupListReqVO customerGroupListReqVO);
}
