<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qudian.lme</groupId>
        <artifactId>matrix-bom</artifactId>
        <version>3.1.1-jdk17-RELEASE</version>
    </parent>
    <groupId>com.qudian.idle</groupId>
    <artifactId>idle-customer-center</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <properties>
        <basic.base.version>1.0.0-SNAPSHOT</basic.base.version>
        <basic.api.version>1.0.0-SNAPSHOT</basic.api.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.qudian.idle</groupId>
                <artifactId>crm-web-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.16</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.idle</groupId>
                <artifactId>idle-customer-center-api</artifactId>
                <version>${basic.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.idle</groupId>
                <artifactId>idle-customer-center-common</artifactId>
                <version>${basic.base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.idle</groupId>
                <artifactId>idle-customer-center-business</artifactId>
                <version>${basic.base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qudian.idle</groupId>
                <artifactId>idle-customer-center-infrastructure</artifactId>
                <version>${basic.base.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--多环境配置begin-->
    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profilesActive>local</profilesActive>
                <dockerTag>local</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <profilesActive>dev</profilesActive>
                <dockerTag>dev</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profilesActive>test</profilesActive>
                <dockerTag>test</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>test2</id>
            <properties>
                <profilesActive>test2</profilesActive>
                <dockerTag>test2</dockerTag>
                <hurbo>newhub-test.fadongxi.com/lme/</hurbo>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profilesActive>prod</profilesActive>
                <dockerTag>latest</dockerTag>
                <hurbo/>
            </properties>
        </profile>
    </profiles>
    <!--多环境配置end-->
    <distributionManagement>
        <repository>
            <!-- ID要和MAVEN中conif/setting.xml 中的server保持一致 -->
            <id>releases</id>
            <name>User Project Release</name>
            <!-- release版本的url地址 -->
            <url>http://***********:8081/repository/maven-releases/</url>
            <layout>default</layout>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>User Project SNAPSHOTS</name>
            <url>http://***********:8081/repository/maven-snapshots/</url>
            <layout>default</layout>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.4.13</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <compilerArgument>-parameters</compilerArgument>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok.mapstruct.binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <!-- due to problem in maven-compiler-plugin, for verbose mode add showWarnings -->
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>
                            -Amapstruct.defaultComponentModel=spring
                        </arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <includeEmptyDirs>true</includeEmptyDirs>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>public</id>
            <name>public repository</name>
            <url>http://***********:8081/repository/maven-public/</url>
            <releases>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
        <repository>
            <id>snapshots</id>
            <name>snapshots repository</name>
            <url>http://***********:8081/repository/maven-snapshots/</url>
            <releases>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
        <repository>
            <id>releases</id>
            <name>releases repository</name>
            <url>http://***********:8081/repository/maven-releases/</url>
            <releases>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>
    </repositories>
    <!--maven仓库配置end-->
    <modules>
        <module>idle-customer-center-infrastructure</module>
        <module>idle-customer-center-server</module>
        <module>idle-customer-center-common</module>
        <module>idle-customer-center-api</module>
        <module>idle-customer-center-business</module>
    </modules>
</project>
