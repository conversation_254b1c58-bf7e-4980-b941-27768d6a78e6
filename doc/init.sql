
create table idle_customer.customer_base
(
    id                     bigint unsigned auto_increment comment '主键' primary key,
    name                   varchar(64)                             not null comment '客户名称',
    mobile                 varchar(64)                             not null comment '客户手机号',
    wechat_id              varchar(64)    default ''                        null comment '客户微信号',
    gender                 tinyint                                 not null comment '性别 1(男), 2(女)',
    age_group              tinyint                                 not null comment '客户年龄段: 1(<24), 2(25-34), 3(35-44), 4(45-54), 5(55+)',
    source                 tinyint                                 not null comment '客户来源: 1(线上引流), 2(线下渠道)',
    home_visit_status      tinyint                                 not null comment '是否接受上门: 1(接受上门), 2(不接受上门)',
    output_intention       tinyint                                 not null comment '客户出售意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定)',
    membership_type        tinyint                                 not null comment '会员类型: 1(普通), 2（VIP会员)',
    purchase_intention     tinyint                                 not null comment '客户购买意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定)',
    province               varchar(128)                             not null comment '地址-省份',
    city                   varchar(128)                             not null comment '地址-城市',
    district               varchar(128)                             not null comment '地址-区域',
    detail_address         varchar(256)                             not null comment '地址-详细地址',
    location               varchar(128)   default ''                null comment '地址-经纬度',
    remark                 varchar(1024)   default ''               null comment '备注',
    images                 varchar(2048)   default ''               null comment '备注图',
    status                 tinyint         default 0                not null comment '状态 0-正常 1-禁用',
    created_by             bigint unsigned                          not null comment '创建人ID',
    created_by_name        varchar(64)                              null comment '创建人姓名',
    related_manager_id     bigint unsigned                          not null comment '关联的客户经理',
    related_manager_name   varchar(64)                              null comment '关联的客户经理姓名',
    related_manager_updated_time datetime  default CURRENT_TIMESTAMP  not null comment '关联修改时间',
    delete_flag            tinyint       default 0                 not null comment '是否删除 1已删除 0 未删除',
    created_time             datetime      default CURRENT_TIMESTAMP not null,
    updated_time             datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)   comment '奢侈品-客户基础信息表' charset = utf8mb4;

create index idx_customer_base_related_manager_id on idle_customer.customer_base (related_manager_id);
create index idx_customer_base_name on idle_customer.customer_base (name);
create unique index uniq_customer_base_mobile on idle_customer.customer_base (mobile);



create table idle_customer.customer_ext
(
    id                     bigint unsigned auto_increment comment '主键' primary key,
    customer_id            bigint unsigned                          not null comment '客户ID',
    type                   tinyint                                  not null comment '附属类型: 1(沟通方式), 2(偏好类型), 3(偏好品牌)',
    ext_value              json                                     not null comment '附属值',
    delete_flag            tinyint       default 0                 not null comment '是否删除 1已删除 0 未删除',
    created_time             datetime      default CURRENT_TIMESTAMP not null,
    updated_time             datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)   comment '奢侈品-客户附属表' charset = utf8mb4;

create index idx_customer_ext_customer_id on idle_customer.customer_ext (customer_id);


create table  idle_customer.customer_manager_log
(
    id                     bigint unsigned auto_increment comment '主键' primary key,
    customer_id            bigint unsigned                          not null comment '客户ID',
    related_manager_id     bigint unsigned                          not null comment '关联的客户经理',
    related_manager_name   varchar(64)                              null comment '关联的客户经理姓名',
    updated_by             bigint unsigned                          not null comment '修改人ID',
    remark                 varchar(1024)   default ''               null comment '备注',
    created_time             datetime      default CURRENT_TIMESTAMP not null,
    updated_time             datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)   comment '奢侈品-关联客户经理变更日志表' charset = utf8mb4;

create index idx_customer_manager_log_related_manager_id on idle_customer.customer_manager_log (related_manager_id);

create table  idle_customer.customer_group
(
    id                     bigint unsigned auto_increment comment '主键' primary key,
    group_name             varchar(64)                             not null comment '客户组名称',
    manager_id             bigint unsigned                          not null comment '客户经理ID',
    remark                 varchar(1024)   default ''               null comment '备注',
    delete_flag            tinyint       default 0                 not null comment '是否删除 1已删除 0 未删除',
    created_time            datetime      default CURRENT_TIMESTAMP not null,
    updated_time             datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)   comment '奢侈品-客户组别表' charset = utf8mb4;

create index idx_customer_group_manager_id on idle_customer.customer_group (manager_id);

create table  idle_customer.customer_group_relation
(
    id                     bigint unsigned auto_increment comment '主键' primary key,
    customer_id            bigint unsigned                          not null comment '客户ID',
    group_id               bigint unsigned                          not null comment '客户组ID',
    manager_id             bigint unsigned                          not null comment '客户经理ID',
    remark                 varchar(1024)   default ''               null comment '备注',
    delete_flag            tinyint       default 0                 not null comment '是否删除 1已删除 0 未删除',
    created_time             datetime      default CURRENT_TIMESTAMP not null,
    updated_time             datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)   comment '奢侈品-客户关联组别表' charset = utf8mb4;

create index idx_customer_group_relation_customer_id on idle_customer.customer_group_relation (customer_id);
