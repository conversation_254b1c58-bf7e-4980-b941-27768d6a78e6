package com.qudian.idle.customer.center.server.facade.impl.app;

import com.alibaba.fastjson.JSON;
import com.qudian.idle.customer.center.api.facade.app.CustomerGroupAppFacade;
import com.qudian.idle.customer.center.api.facade.app.CustomerPropertyAppFacade;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupJoinReqVO;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupListReqVO;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupSaveReqVO;
import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyRespVO;
import com.qudian.idle.customer.center.api.vo.response.group.CustomerGroupJoinRespVO;
import com.qudian.idle.customer.center.api.vo.response.group.CustomerGroupListRespVO;
import com.qudian.idle.customer.center.api.vo.share.IdRequestVO;
import com.qudian.idle.customer.center.business.service.base.CustomerGroupService;
import com.qudian.lme.base.builder.ResponseBuilder;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR> Huang
 * @date 2025/8/13
 */
@Service(version = "1.0.0")
@Slf4j
@Api(value = "客户组管理-app")
@Validated
@RestController
public class CustomerGroupAppFacadeImpl implements CustomerGroupAppFacade {
    @Resource
    private CustomerGroupService customerGroupService;

    @Override
    @ApiOperation(value = "首页查询条件-获取客户配置属性")
    @PostMapping("/group/join")
    public BaseResponseDTO<IdRequestVO> join(@RequestBody CustomerGroupJoinReqVO customerGroupJoinReqVO) {
        log.info("op=start_CustomerGroupAppFacadeImpl.join, customerGroupJoinReqVO={}", customerGroupJoinReqVO);
        return ResponseBuilder.buildSuccess(customerGroupService.join(customerGroupJoinReqVO));
    }

    @Override
    @PostMapping("/group/save")
    public BaseResponseDTO<IdRequestVO> save(@RequestBody CustomerGroupSaveReqVO customerGroupSaveReqVO) {
        log.info("op=start_CustomerGroupAppFacadeImpl.save, customerGroupSaveReqVO={}", customerGroupSaveReqVO);
        return ResponseBuilder.buildSuccess(customerGroupService.save(customerGroupSaveReqVO));
    }

    @Override
    @PostMapping("/group/list")
    public BaseResponseDTO<CustomerGroupListRespVO> list(@RequestBody CustomerGroupListReqVO customerGroupListReqVO) {
        log.info("op=start_CustomerGroupAppFacadeImpl.list, customerGroupListReqVO={}", customerGroupListReqVO);
        return ResponseBuilder.buildSuccess(customerGroupService.list(customerGroupListReqVO));
    }
}
