package com.qudian.idle.customer.center.server.facade.impl.mis;

import com.qudian.idle.customer.center.api.facade.mis.CustomerBaseMisFacade;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerShowReqVO;
import com.qudian.idle.customer.center.api.vo.request.mis.CustomerMisPageReqVO;
import com.qudian.idle.customer.center.api.vo.request.mis.CustomerMisTransferReqVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.api.vo.response.mis.CustomerMisPageRespVO;
import com.qudian.idle.customer.center.api.vo.share.IdRequestVO;
import com.qudian.idle.customer.center.business.service.base.CustomerBaseService;
import com.qudian.lme.base.builder.ResponseBuilder;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import com.qudian.pdt.api.toolkit.vo.response.PagingList;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <p>文件名称:com.qudian.idle.customer.center.server.facade.impl.mis.CustomerBaseMisFacadeImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/16
 */
@DubboService(version = "1.0.0")
public class CustomerBaseMisFacadeImpl implements CustomerBaseMisFacade{
    @Resource
    private CustomerBaseService customerBaseService;

    @Override
    public BaseResponseDTO<IdRequestVO> addOrEdit(CustomerEditReqVO reqVO) {
        return ResponseBuilder.buildSuccess(customerBaseService.createOrUpdate(reqVO));
    }

    @Override
    public BaseResponseDTO<CustomerShowRespVO> show(CustomerShowReqVO reqVO) {
        return ResponseBuilder.buildSuccess(customerBaseService.query(reqVO));
    }

    @Override
    public BaseResponseDTO<PagingList<CustomerMisPageRespVO>> page(CustomerMisPageReqVO reqVO) {
        return ResponseBuilder.buildSuccess(customerBaseService.page(reqVO));
    }

    @Override
    public BaseResponseDTO<BaseResponseVO> transfer(CustomerMisTransferReqVO reqVO) {
        customerBaseService.transfer(reqVO);
        return ResponseBuilder.buildSuccess(new BaseResponseVO());
    }
}
