package com.qudian.idle.customer.center.server.weaver;

import com.qudian.pdt.toolkit.container.core.weaver.FacadeWeaver;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <p>文件描述: 门面切点</p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> architecture board)
 * @version 1.0
 */
@Aspect
@Component
public class FacadeExtWeaver extends FacadeWeaver {

    @Pointcut("execution(* com.qudian.idle.customer.center.server.facade..*(..))")
    protected void exPointCut() {}

}
