package com.qudian.idle.customer.center.server.facade.impl.app;

import com.alibaba.fastjson.JSON;
import com.qudian.idle.customer.center.api.facade.app.CustomerPropertyAppFacade;
import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyRespVO;
import com.qudian.lme.base.builder.ResponseBuilder;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR> Huang
 * @date 2025/8/13
 */
@Service(version = "1.0.0")
@Slf4j
@Api(tags = "配置属性管理")
@Validated
@RestController
public class CustomerPropertyAppFacadeImpl implements CustomerPropertyAppFacade {

    @Value("${customer.properties:}")
    private String customerProperties;



    @Override
    @ApiOperation(value = "获取客户配置属性")
    @PostMapping("/base/properties")
    public BaseResponseDTO<CustomerPropertyRespVO> getPropertyList(@RequestBody BaseRequestVO requestVO) {
        log.info("op=start_CustomerConfigAppFacadeImpl.getConfigList, requestVO={},customerProperties={}", requestVO,customerProperties);
        CustomerPropertyRespVO customerConfigRespVO = null;
        try {
            customerConfigRespVO = JSON.parseObject(customerProperties, CustomerPropertyRespVO.class);
        } catch (Exception e) {
            log.error("[op=CustomerPropertyAppFacadeImpl.getConfigList error]错误信息:{}",e);
        }
        return ResponseBuilder.buildSuccess(Objects.isNull(customerConfigRespVO) ? new CustomerPropertyRespVO() : customerConfigRespVO);
    }


}
