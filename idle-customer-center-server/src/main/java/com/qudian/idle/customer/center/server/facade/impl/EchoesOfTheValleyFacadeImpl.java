package com.qudian.idle.customer.center.server.facade.impl;

import com.qudian.idle.customer.center.api.facade.EchoesOfTheValleyFacade;
import com.qudian.idle.customer.center.api.vo.request.ValleyEchosReqVO;
import com.qudian.lme.base.builder.ResponseBuilder;
import com.qudian.lme.common.dto.BaseResponseDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>文件描述: 测试服务连通性</p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> architecture board)
 * @version 1.0
 */
@org.apache.dubbo.config.annotation.DubboService
@Slf4j
public class EchoesOfTheValleyFacadeImpl implements EchoesOfTheValleyFacade {
    @Override
    public BaseResponseDTO<ValleyEchosReqVO> echo(ValleyEchosReqVO reqVO) {
        log.info("Valley echos hey roar: {}", reqVO.getHeyRoar());
        ValleyEchosReqVO res = new ValleyEchosReqVO();
        res.setHeyRoar("[Server_echo] hi:" + reqVO.getHeyRoar());
        res.setOrigin(reqVO.getOrigin());
        return ResponseBuilder.buildSuccess(res);
    }
}
