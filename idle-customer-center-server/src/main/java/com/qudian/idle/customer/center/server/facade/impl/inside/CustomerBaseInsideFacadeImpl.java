package com.qudian.idle.customer.center.server.facade.impl.inside;

import com.qudian.idle.customer.center.api.facade.inside.CustomerBaseInsideFacade;
import com.qudian.idle.customer.center.api.vo.request.inside.CustomerBatchQueryInHomeReqVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerInfoInHomeRespVO;
import com.qudian.idle.customer.center.business.service.base.CustomerBaseService;
import com.qudian.idle.customer.center.infrastructure.repository.CustomerBaseRepository;
import com.qudian.lme.base.builder.ResponseBuilder;
import com.qudian.lme.common.dto.BaseResponseDTO;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.customer.center.server.facade.impl.inside.CustomerBaseInsideFacadeImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/16
 */
@DubboService(version = "1.0.0")
public class CustomerBaseInsideFacadeImpl implements CustomerBaseInsideFacade {

    @Resource
    private CustomerBaseService customerBaseService;

    @Override
    public BaseResponseDTO<List<CustomerInfoInHomeRespVO>> batchQueryCustomerInfoInHome(CustomerBatchQueryInHomeReqVO reqVO) {
        return ResponseBuilder.buildSuccess(customerBaseService.batchQueryCustomerInfoInHome(reqVO));
    }
}
