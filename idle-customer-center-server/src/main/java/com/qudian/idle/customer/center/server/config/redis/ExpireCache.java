package com.qudian.idle.customer.center.server.config.redis;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> architecture board)
 * @version 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "expire.cache")
public class ExpireCache {
    private final Map<String, Duration> initCaches = new HashMap();

    public ExpireCache() {
    }

    public Map<String, Duration> getInitCaches() {
        return this.initCaches;
    }
}
