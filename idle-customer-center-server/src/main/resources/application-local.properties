logging.config=classpath:log4j2-spring-local.xml
logging.level.com.qudian.wanwu.shop.tripod.dao=debug
server.port=8082

logging.level.com.qudian.idle.customer.center.infrastructure.repository.database.mapper=debug


spring.datasource.url = *************************************************************************************************************************************************************************************************************************************************************
spring.datasource.username = qdfresh_test
spring.datasource.password = ZYF5gThaUNdf4QV6
spring.datasource.type = com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
spring.datasource.jdbc-url = *************************************************************************************************************************************************************************************************************************************************************

spring.data.redis.host = lme-dev1-o.redis.zhangbei.rds.aliyuncs.com
spring.data.redis.block-when-exhausted = false
spring.data.redis.database = 12
spring.data.redis.port = 6379
spring.data.redis.jedis.pool.max-active = 200
spring.data.redis.jedis.pool.max-wait = 10000
spring.data.redis.jedis.pool.max-idle = 8
spring.data.redis.jedis.pool.min-idle = 0
spring.data.redis.timeout = 10000
spring.cache.redis.time-to-live = 600
spring.data.redis.password = JQgFNrilA86vGRjh

dubbo.protocol.dubbo.payload = 8388608
dubbo.application.name = ${spring.application.name}
dubbo.registry.address = zookeeper://localhost:2181
dubbo.registry.timeout = 6000
dubbo.metadata-report.address = zookeeper://localhost:2181
dubbo.protocol.name = dubbo
dubbo.protocol.port = 34066
dubbo.scan.base-packages = com.qudian.*
dubbo.reference.check = false
dubbo.consumer.check = false
dubbo.consumer.timeout = 5000
dubbo.consumer.retries = 0
dubbo.registry.check = false
dubbo.provider.threads = 400
dubbo.provider.retries = 0
dubbo.provider.version = 1.0.0
dubbo.reference.default.version = 1.0.0
dubbo.provider.validation = jvalidationNew
dubbo.counsumer.validation = jvalidationNew
#dubbo.provider.filter = -exception,-validation,generic,dubboExceptionFilter,dubboValidationFilter,default,dubboI18nFilter


customer.properties={\
    "properties": {\
        "membershipType": [\
                { "code": "1", "title": "\u666E\u901A" }, { "code": "2", "title": "VIP\u4F1A\u5458" }\
            ],\
        "customerType": [\
                { "code": "organization", "title": "\u7EC4\u7EC7" }, { "code": "personal", "title": "\u4E2A\u4EBA" }\
            ],\
        "gender": [\
                { "code": "1", "title": "\u7537" }, { "code": "2", "title": "\u5973" }\
            ]\
    }\
}

customer.search.properties={\
    "properties": {\
        "membershipType": [\
                { "code": "-1", "title": "\u5168\u90E8" },{ "code": "1", "title": "\u666E\u901A" }, { "code": "2", "title": "VIP\u4F1A\u5458" }\
            ],\
        "customerType": [\
                { "code": "-1", "title": "\u5168\u90E8" },{ "code": "organization", "title": "\u7EC4\u7EC7" }, { "code": "personal", "title": "\u4E2A\u4EBA" }\
            ]\
    }\
}
