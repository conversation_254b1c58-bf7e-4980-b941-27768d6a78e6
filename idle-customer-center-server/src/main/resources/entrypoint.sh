#!/bin/sh
mkdir -p /data/idle/idle-customer-center-server/`hostname`/   /data/logs/idle/
ln -s /data/idle/idle-customer-center-server/`hostname`/ /data/logs/idle/idle-customer-center-server

trap_handler() {
  curl "localhost:22222/offline"
  sleep 15s
  echo '准备kill java进程'
  ps -ef | grep java | grep idle-customer-center-server | awk '{print $2}' | xargs kill
}

trap 'trap_handler' 15

java -Dfile.encoding=UTF-8 $JAVA_OPTS -javaagent:/usr/local/skywalking/agent/skywalking-agent.jar=$SW_PARAMS -jar /usr/local/$JAR_FILE &

while true
do
  sleep 5
  pidof java||exit 1
done
