#spring
spring.application.name=idle-customer-server
server.servlet.context-path=/api/customer
spring.messages.encoding=UTF-8
spring.profiles.active=local
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
server.port=8080
#dubbo
dubbo.protocol.accesslog=true
#dubbo.application.logger=log4j2
#apollo
app.id=idle-customer-server
management.endpoints.web.exposure.include=info,health
management.server.port = 9145
